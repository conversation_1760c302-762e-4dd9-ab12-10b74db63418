// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
//@version=5

strategy("IB Breakout Strategy", shorttitle="IB Strategy", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=10)

daySess = input.session("0930-1600","Session Period",options=["0930-1600","0700-1030","0720-1030","0915-1530"])
ibSess = input.session("0930-1030", "Initial Balance", options=["0930-0945", "0930-1000", "0930-1030","0700-0800","0720-0930","1330-1600","0200-0500","1200-1330","0915-0945"])
show_extension = input.bool(true,"Show Extension","Display extended bars on the second day")

// Strategy inputs
use_stop_loss = input.bool(true, "Use Stop Loss", "Enable stop loss at opposite IB level")
risk_reward_ratio = input.float(2.0, "Risk/Reward Ratio", minval=0.5, maxval=5.0, tooltip="Risk reward ratio for position sizing")
max_trades_per_day = input.int(2, "Max Trades Per Day", minval=1, maxval=10, tooltip="Maximum number of trades per day")

//Bars
is_newbar(sess) =>
    t = time("D", sess, "America/New_York")
    na(t[1]) and not na(t) or t[1] < t

is_session(sess) =>
    not na(time(timeframe.period, sess, "America/New_York"))

nyNewbar = is_newbar(daySess)

bool inInitialBalance = is_session(ibSess)
int ib_opening_bar = na
ib_opening_bar := ib_opening_bar[1]

float ib_high = na
float ib_low = na

float last_ib_high = na
float last_ib_low = na


if inInitialBalance and not inInitialBalance[1]
    last_ib_high := na
    last_ib_low := na
    ib_high := na
    ib_low := na
    ib_opening_bar := bar_index

else if inInitialBalance and inInitialBalance[1] and not inInitialBalance[2]
    last_ib_high := ib_high[3]
    last_ib_low := ib_low[3]
    ib_high := math.max(high,high[1])
    ib_low := math.min(low,low[1])

else if inInitialBalance
    ib_high := math.max(ib_high[1],high)
    ib_low := math.min(ib_low[1],low)
    last_ib_high := last_ib_high[1]
    last_ib_low := last_ib_low[1]

else
    ib_high := ib_high[1]
    ib_low := ib_low[1]
    last_ib_high := last_ib_high[1]
    last_ib_low := last_ib_low[1]


ib_high_line = plot(ib_high,"Initial Balance High",color.gray,2,style=plot.style_linebr)
ib_low_line = plot(ib_low,"Initial Balance Low",color.gray,2,style=plot.style_linebr)

fill(ib_high_line,ib_low_line,color.new(color.gray,95))

// Calculate and plot developing midpoint
float ib_mid = not na(ib_high) and not na(ib_low) ? (ib_high + ib_low)/2 : na

// Variable to store the color of IB mid line
var color ib_mid_color = color.white

// Reset color to white at the start of each new IB session
if inInitialBalance and not inInitialBalance[1]
    ib_mid_color := color.white

// Check if we're at the end of IB session (10:30am candle) and determine color
if inInitialBalance[1] and not inInitialBalance
    // This is the first bar after IB session ends (10:30am candle just closed)
    if not na(ib_mid) and not na(close[1])
        if close[1] > ib_mid
            ib_mid_color := color.green
        else if close[1] < ib_mid
            ib_mid_color := color.red
        else
            ib_mid_color := color.white

ib_mid_line = plot(ib_mid,"Initial Balance Midpoint",ib_mid_color,1,style=plot.style_linebr)

last_ib_high_line = plot(show_extension?last_ib_high:na,"Last Initial Balance High",color.gray,1,style=plot.style_linebr)
last_ib_low_line = plot(show_extension?last_ib_low:na,"Last Initial Balance Low",color.gray,1,style=plot.style_linebr,display=display.price_scale)

// Variables to store labels
var label ib_high_label = na
var label ib_low_label = na
var label ib_mid_label = na
var label last_ib_high_label = na
var label last_ib_low_label = na

// Add labels for Initial Balance lines
if barstate.islast
    // Delete previous labels to prevent snail trail
    if not na(ib_high_label)
        label.delete(ib_high_label)
    if not na(ib_low_label)
        label.delete(ib_low_label)
    if not na(ib_mid_label)
        label.delete(ib_mid_label)
    if not na(last_ib_high_label)
        label.delete(last_ib_high_label)
    if not na(last_ib_low_label)
        label.delete(last_ib_low_label)

    // Create new labels
    if not na(ib_high)
        ib_high_label := label.new(bar_index, ib_high, "IB High", style=label.style_label_left, textcolor=color.gray, size=size.large, color=color.new(color.white, 100))

    if not na(ib_low)
        ib_low_label := label.new(bar_index, ib_low, "IB Low", style=label.style_label_left, textcolor=color.gray, size=size.large, color=color.new(color.white, 100))

    if not na(ib_mid)
        ib_mid_label := label.new(bar_index, ib_mid, "IB Mid", style=label.style_label_left, textcolor=ib_mid_color, size=size.large, color=color.new(color.white, 100))

    if show_extension and not na(last_ib_high)
        last_ib_high_label := label.new(bar_index, last_ib_high, "Last IB High", style=label.style_label_left, textcolor=color.gray, size=size.large, color=color.new(color.white, 100))

    if show_extension and not na(last_ib_low)
        last_ib_low_label := label.new(bar_index, last_ib_low, "Last IB Low", style=label.style_label_left, textcolor=color.gray, size=size.large, color=color.new(color.white, 100))

// Vertical lines at 9:30, 10:30, and 11:30
var line start_line = na
var line end_line = na
var line hour_line = na

// Check for 11:30 time
bool is1130 = hour(time, "America/New_York") == 11 and minute(time, "America/New_York") == 30

if inInitialBalance and not inInitialBalance[1]
    // Draw vertical line at 9:30 (start of initial balance)
    start_line := line.new(bar_index, low, bar_index, high, extend=extend.both, color=color.white, style=line.style_dotted, width=1)

if inInitialBalance[1] and not inInitialBalance
    // Draw vertical line at 10:30 (end of initial balance)
    end_line := line.new(bar_index, low, bar_index, high, extend=extend.both, color=color.white, style=line.style_dotted, width=1)

if is1130 and not is1130[1]
    // Draw vertical line at 11:30
    hour_line := line.new(bar_index, low, bar_index, high, extend=extend.both, color=color.white, style=line.style_dotted, width=1)

// Calculate IB range and extension levels
float ib_range = not na(ib_high) and not na(ib_low) ? ib_high - ib_low : na

// Calculate 25% and 50% extensions above IB high
float ib_high_25_ext = not na(ib_high) and not na(ib_range) ? ib_high + (ib_range * 0.25) : na
float ib_high_50_ext = not na(ib_high) and not na(ib_range) ? ib_high + (ib_range * 0.50) : na

// Calculate 25% and 50% extensions below IB low
float ib_low_25_ext = not na(ib_low) and not na(ib_range) ? ib_low - (ib_range * 0.25) : na
float ib_low_50_ext = not na(ib_low) and not na(ib_range) ? ib_low - (ib_range * 0.50) : na

// Plot extension lines
ib_high_25_ext_line = plot(ib_high_25_ext, "IB High 25% Extension", color.new(color.gray, 60), 1, style=plot.style_linebr)
ib_high_50_ext_line = plot(ib_high_50_ext, "IB High 50% Extension", color.new(color.gray, 40), 1, style=plot.style_linebr)
ib_low_25_ext_line = plot(ib_low_25_ext, "IB Low 25% Extension", color.new(color.gray, 60), 1, style=plot.style_linebr)
ib_low_50_ext_line = plot(ib_low_50_ext, "IB Low 50% Extension", color.new(color.gray, 40), 1, style=plot.style_linebr)

// Variables to store extension labels
var label ib_high_25_label = na
var label ib_high_50_label = na
var label ib_low_25_label = na
var label ib_low_50_label = na

// Add labels for extension lines
if barstate.islast
    // Delete previous extension labels to prevent snail trail
    if not na(ib_high_25_label)
        label.delete(ib_high_25_label)
    if not na(ib_high_50_label)
        label.delete(ib_high_50_label)
    if not na(ib_low_25_label)
        label.delete(ib_low_25_label)
    if not na(ib_low_50_label)
        label.delete(ib_low_50_label)

    // Create new extension labels
    if not na(ib_high_25_ext)
        ib_high_25_label := label.new(bar_index, ib_high_25_ext, "IB High +25%", style=label.style_label_left, textcolor=color.new(color.gray, 60), size=size.large, color=color.new(color.white, 100))

    if not na(ib_high_50_ext)
        ib_high_50_label := label.new(bar_index, ib_high_50_ext, "IB High +50%", style=label.style_label_left, textcolor=color.new(color.gray, 40), size=size.large, color=color.new(color.white, 100))

    if not na(ib_low_25_ext)
        ib_low_25_label := label.new(bar_index, ib_low_25_ext, "IB Low -25%", style=label.style_label_left, textcolor=color.new(color.gray, 60), size=size.large, color=color.new(color.white, 100))

    if not na(ib_low_50_ext)
        ib_low_50_label := label.new(bar_index, ib_low_50_ext, "IB Low -50%", style=label.style_label_left, textcolor=color.new(color.gray, 40), size=size.large, color=color.new(color.white, 100))

// ============================================================================
// STRATEGY LOGIC
// ============================================================================

// Track daily trade count
var int daily_trade_count = 0
var int last_trade_day = 0

// Reset daily trade count on new day
if nyNewbar
    daily_trade_count := 0
    last_trade_day := dayofweek

// Strategy conditions
bool ib_session_ended = inInitialBalance[1] and not inInitialBalance
bool can_trade = daily_trade_count < max_trades_per_day and not na(ib_high) and not na(ib_low) and not na(ib_high_25_ext) and not na(ib_low_25_ext)
bool after_ib_session = not inInitialBalance and not na(ib_high) and not na(ib_low)

// Track breakout state
var bool ib_high_broken = false
var bool ib_low_broken = false
var bool ib_high_retested = false
var bool ib_low_retested = false
var int retest_time_high = 0
var int retest_time_low = 0

// Reset breakout tracking at start of new day
if nyNewbar
    ib_high_broken := false
    ib_low_broken := false
    ib_high_retested := false
    ib_low_retested := false
    retest_time_high := 0
    retest_time_low := 0

// Variables for entry criteria labels
var label breakout_label = na
var label retest_label = na
var label engulf_label = na

// Track breakouts (close above/below IB levels)
if after_ib_session and close > ib_high and close[1] <= ib_high
    ib_high_broken := true
    // Create breakout label
    if not na(breakout_label)
        label.delete(breakout_label)
    breakout_label := label.new(bar_index, high + (ib_range * 0.02), "BREAKOUT ↑",
                               style=label.style_label_down,
                               color=color.new(color.green, 20),
                               textcolor=color.white,
                               size=size.normal)

if after_ib_session and close < ib_low and close[1] >= ib_low
    ib_low_broken := true
    // Create breakout label
    if not na(breakout_label)
        label.delete(breakout_label)
    breakout_label := label.new(bar_index, low - (ib_range * 0.02), "BREAKOUT ↓",
                               style=label.style_label_up,
                               color=color.new(color.red, 20),
                               textcolor=color.white,
                               size=size.normal)

// Track retests after breakout
if ib_high_broken and not ib_high_retested and low <= ib_high and low[1] > ib_high
    ib_high_retested := true
    retest_time_high := time
    // Create retest label
    if not na(retest_label)
        label.delete(retest_label)
    retest_label := label.new(bar_index, low - (ib_range * 0.02), "RETEST ✓",
                             style=label.style_label_up,
                             color=color.new(color.blue, 20),
                             textcolor=color.white,
                             size=size.normal)

if ib_low_broken and not ib_low_retested and high >= ib_low and high[1] < ib_low
    ib_low_retested := true
    retest_time_low := time
    // Create retest label
    if not na(retest_label)
        label.delete(retest_label)
    retest_label := label.new(bar_index, high + (ib_range * 0.02), "RETEST ✓",
                             style=label.style_label_down,
                             color=color.new(color.blue, 20),
                             textcolor=color.white,
                             size=size.normal)

// Check for engulfing candle within 5 minutes of retest on current timeframe
bool bullish_engulf = close > open and close > high[1] and open < low[1]
bool bearish_engulf = close < open and close < low[1] and open > high[1]

// Get 2-minute timeframe data for engulfing patterns
[tf2m_open, tf2m_high, tf2m_low, tf2m_close] = request.security(syminfo.tickerid, "2", [open, high, low, close])
bool tf2m_bullish_engulf = tf2m_close > tf2m_open and tf2m_close > tf2m_high[1] and tf2m_open < tf2m_low[1]
bool tf2m_bearish_engulf = tf2m_close < tf2m_open and tf2m_close < tf2m_low[1] and tf2m_open > tf2m_high[1]

// Long condition: IB high broken, retested, and engulf (current TF OR 2m TF) within 5 minutes
bool current_tf_long_engulf = ib_high_retested and bullish_engulf and (time - retest_time_high) <= 300000
bool tf2m_long_engulf = ib_high_retested and tf2m_bullish_engulf and (time - retest_time_high) <= 300000
bool long_engulf_valid = current_tf_long_engulf or tf2m_long_engulf
bool long_condition = after_ib_session and can_trade and long_engulf_valid and strategy.position_size == 0

// Short condition: IB low broken, retested, and engulf (current TF OR 2m TF) within 5 minutes
bool current_tf_short_engulf = ib_low_retested and bearish_engulf and (time - retest_time_low) <= 300000
bool tf2m_short_engulf = ib_low_retested and tf2m_bearish_engulf and (time - retest_time_low) <= 300000
bool short_engulf_valid = current_tf_short_engulf or tf2m_short_engulf
bool short_condition = after_ib_session and can_trade and short_engulf_valid and strategy.position_size == 0

// Store engulfing candle levels for stop loss
var float long_stop_level = na
var float short_stop_level = na

// Entry logic
if long_condition
    long_stop_level := low  // Stop at low of engulfing candle
    strategy.entry("Long", strategy.long, comment="IB High Breakout + Engulf")
    daily_trade_count := daily_trade_count + 1
    // Create engulfing label for long entry
    if not na(engulf_label)
        label.delete(engulf_label)
    engulf_label := label.new(bar_index, low - (ib_range * 0.04), "ENGULF ⚡",
                             style=label.style_label_up,
                             color=color.new(color.orange, 20),
                             textcolor=color.white,
                             size=size.large)

if short_condition
    short_stop_level := high  // Stop at high of engulfing candle
    strategy.entry("Short", strategy.short, comment="IB Low Breakout + Engulf")
    daily_trade_count := daily_trade_count + 1
    // Create engulfing label for short entry
    if not na(engulf_label)
        label.delete(engulf_label)
    engulf_label := label.new(bar_index, high + (ib_range * 0.04), "ENGULF ⚡",
                             style=label.style_label_down,
                             color=color.new(color.orange, 20),
                             textcolor=color.white,
                             size=size.large)

// Exit logic - separate from entry to ensure proper execution
if strategy.position_size > 0 and not na(ib_high_25_ext)
    if use_stop_loss and not na(long_stop_level)
        strategy.exit("Long Exit", "Long", stop=long_stop_level, limit=ib_high_25_ext, comment="TP/SL Engulf")
    else
        strategy.exit("Long Exit", "Long", limit=ib_high_25_ext, comment="Take Profit")

if strategy.position_size < 0 and not na(ib_low_25_ext)
    if use_stop_loss and not na(short_stop_level)
        strategy.exit("Short Exit", "Short", stop=short_stop_level, limit=ib_low_25_ext, comment="TP/SL Engulf")
    else
        strategy.exit("Short Exit", "Short", limit=ib_low_25_ext, comment="Take Profit")

// Trade signals are automatically shown by strategy entries/exits
// Removed plotshape to avoid duplicate signals

// Plot strategy info
var table info_table = table.new(position.top_right, 2, 4, bgcolor=color.white, border_width=1)
if barstate.islast
    table.cell(info_table, 0, 0, "Daily Trades:", text_color=color.black)
    table.cell(info_table, 1, 0, str.tostring(daily_trade_count) + "/" + str.tostring(max_trades_per_day), text_color=color.black)
    table.cell(info_table, 0, 1, "Position:", text_color=color.black)
    table.cell(info_table, 1, 1, strategy.position_size > 0 ? "Long" : strategy.position_size < 0 ? "Short" : "Flat", text_color=color.black)
    table.cell(info_table, 0, 2, "IB Range:", text_color=color.black)
    table.cell(info_table, 1, 2, not na(ib_range) ? str.tostring(ib_range, "#.##") : "N/A", text_color=color.black)
    table.cell(info_table, 0, 3, "Next Target:", text_color=color.black)
    next_target = strategy.position_size > 0 ? ib_high_25_ext : strategy.position_size < 0 ? ib_low_25_ext : na
    table.cell(info_table, 1, 3, not na(next_target) ? str.tostring(next_target, "#.##") : "N/A", text_color=color.black)

// Entry Criteria Status Panel
var table status_table = table.new(position.top_left, 2, 5, bgcolor=color.new(color.gray, 90), border_width=1)
if barstate.islast and after_ib_session
    // Header
    table.cell(status_table, 0, 0, "Entry Criteria", text_color=color.white, bgcolor=color.new(color.blue, 20), text_size=size.normal)
    table.cell(status_table, 1, 0, "Status", text_color=color.white, bgcolor=color.new(color.blue, 20), text_size=size.normal)

    // Step 1: Breakout
    breakout_status = (ib_high_broken or ib_low_broken) ? "✓ COMPLETE" : "⏳ WAITING"
    breakout_color = (ib_high_broken or ib_low_broken) ? color.green : color.orange
    table.cell(status_table, 0, 1, "1. Breakout", text_color=color.white, text_size=size.small)
    table.cell(status_table, 1, 1, breakout_status, text_color=breakout_color, text_size=size.small)

    // Step 2: Retest
    retest_status = (ib_high_retested or ib_low_retested) ? "✓ COMPLETE" : (ib_high_broken or ib_low_broken) ? "⏳ WAITING" : "❌ PENDING"
    retest_color = (ib_high_retested or ib_low_retested) ? color.green : (ib_high_broken or ib_low_broken) ? color.orange : color.red
    table.cell(status_table, 0, 2, "2. Retest", text_color=color.white, text_size=size.small)
    table.cell(status_table, 1, 2, retest_status, text_color=retest_color, text_size=size.small)

    // Step 3: Engulfing
    engulf_status = (long_engulf_valid or short_engulf_valid) ? "✓ COMPLETE" : (ib_high_retested or ib_low_retested) ? "⏳ WAITING" : "❌ PENDING"
    engulf_color = (long_engulf_valid or short_engulf_valid) ? color.green : (ib_high_retested or ib_low_retested) ? color.orange : color.red
    table.cell(status_table, 0, 3, "3. Engulfing", text_color=color.white, text_size=size.small)
    table.cell(status_table, 1, 3, engulf_status, text_color=engulf_color, text_size=size.small)

    // Overall Status
    overall_status = (long_condition or short_condition) ? "🚀 ENTRY READY" : "⏳ WAITING"
    overall_color = (long_condition or short_condition) ? color.lime : color.yellow
    table.cell(status_table, 0, 4, "Entry Signal", text_color=color.white, text_size=size.small)
    table.cell(status_table, 1, 4, overall_status, text_color=overall_color, text_size=size.small)
